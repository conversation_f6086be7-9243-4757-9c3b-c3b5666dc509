******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jun 25 21:01:24 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000395


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000004e8  0001fb18  R  X
  SRAM                  20200000   00008000  00000211  00007def  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000004e8   000004e8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000003e8   000003e8    r-x .text
  000004a8    000004a8    00000008   00000008    r-- .rodata
  000004b0    000004b0    00000038   00000038    r-- .cinit
20200000    20200000    00000011   00000000    rw-
  20200000    20200000    00000010   00000000    rw- .data
  20200010    20200010    00000001   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000003e8     
                  000000c0    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000015a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000015c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000001d8    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000022c    00000044     scheduler.o (.text.scheduler_run)
                  00000270    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000002b0    00000040     clock.o (.text.SysTick_Init)
                  000002f0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000032c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000360    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000394    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000003bc    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000003e2    00000002     --HOLE-- [fill = 0]
                  000003e4    00000020     empty.o (.text.main)
                  00000404    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000041a    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000042e    00000002     --HOLE-- [fill = 0]
                  00000430    00000014     scheduler.o (.text.scheduler_init)
                  00000444    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00000456    00000002     --HOLE-- [fill = 0]
                  00000458    00000010     interrupt.o (.text.SysTick_Handler)
                  00000468    0000000c     interrupt.o (.text.GROUP1_IRQHandler)
                  00000474    0000000c     empty.o (.text.led_blink_task)
                  00000480    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000048a    00000002     --HOLE-- [fill = 0]
                  0000048c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000494    00000006     libc.a : exit.c.obj (.text:abort)
                  0000049a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000049e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000004a2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000004a6    00000002     --HOLE-- [fill = 0]

.cinit     0    000004b0    00000038     
                  000004b0    0000000e     (.cinit..data.load) [load image, compression = lzss]
                  000004be    00000002     --HOLE-- [fill = 0]
                  000004c0    0000000c     (__TI_handler_table)
                  000004cc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000004d4    00000010     (__TI_cinit_table)
                  000004e4    00000004     --HOLE-- [fill = 0]

.rodata    0    000004a8    00000008     
                  000004a8    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000004aa    00000006     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000010     UNINITIALIZED
                  20200000    0000000c     scheduler.o (.data.scheduler_task)
                  2020000c    00000004     clock.o (.data.sys_tick)

.bss       0    20200010    00000001     UNINITIALIZED
                  20200010    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             272    2         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       scheduler.o                    88     0         13     
       empty.o                        44     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         410    194       13     
                                                              
    .\System\
       clock.o                        64     0         4      
       interrupt.o                    28     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         92     0         4      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       38     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         48     0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      50        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   990    244       529    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000004d4 records: 2, size/record: 8, table size: 16
	.data: load addr=000004b0, load size=0000000e bytes, run addr=20200000, run size=00000010 bytes, compression=lzss
	.bss: load addr=000004cc, load size=00000008 bytes, run addr=20200010, run size=00000001 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000004c0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000015b  ADC0_IRQHandler               
0000015b  ADC1_IRQHandler               
0000015b  AES_IRQHandler                
0000049a  C$$EXIT                       
0000015b  CANFD0_IRQHandler             
0000015b  DAC0_IRQHandler               
00000481  DL_Common_delayCycles         
000003bd  DL_I2C_setClockConfig         
0000015b  DMA_IRQHandler                
0000015b  Default_Handler               
0000015b  GROUP0_IRQHandler             
00000469  GROUP1_IRQHandler             
0000049b  HOSTexit                      
0000015b  HardFault_Handler             
0000015b  I2C0_IRQHandler               
0000015b  I2C1_IRQHandler               
0000015b  NMI_Handler                   
0000015b  PendSV_Handler                
0000015b  RTC_IRQHandler                
0000049f  Reset_Handler                 
0000015b  SPI0_IRQHandler               
0000015b  SPI1_IRQHandler               
0000015b  SVC_Handler                   
0000032d  SYSCFG_DL_GPIO_init           
000001d9  SYSCFG_DL_I2C_OLED_init       
00000271  SYSCFG_DL_SYSCTL_init         
0000041b  SYSCFG_DL_init                
00000361  SYSCFG_DL_initPower           
00000459  SysTick_Handler               
000002b1  SysTick_Init                  
0000015b  TIMA0_IRQHandler              
0000015b  TIMA1_IRQHandler              
0000015b  TIMG0_IRQHandler              
0000015b  TIMG12_IRQHandler             
0000015b  TIMG6_IRQHandler              
0000015b  TIMG7_IRQHandler              
0000015b  TIMG8_IRQHandler              
0000015b  UART0_IRQHandler              
0000015b  UART1_IRQHandler              
0000015b  UART2_IRQHandler              
0000015b  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000004d4  __TI_CINIT_Base               
000004e4  __TI_CINIT_Limit              
000004e4  __TI_CINIT_Warm               
000004c0  __TI_Handler_Table_Base       
000004cc  __TI_Handler_Table_Limit      
000002f1  __TI_auto_init_nobinit_nopinit
0000015d  __TI_decompress_lzss          
00000445  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000405  __TI_zero_init_nomemset       
0000048d  __aeabi_memcpy                
0000048d  __aeabi_memcpy4               
0000048d  __aeabi_memcpy8               
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000395  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000004a3  _system_pre_init              
00000495  abort                         
ffffffff  binit                         
00000000  interruptVectors              
00000475  led_blink_task                
000003e5  main                          
000000c1  memcpy                        
00000431  scheduler_init                
0000022d  scheduler_run                 
2020000c  sys_tick                      
20200010  task_num                      


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  memcpy                        
0000015b  ADC0_IRQHandler               
0000015b  ADC1_IRQHandler               
0000015b  AES_IRQHandler                
0000015b  CANFD0_IRQHandler             
0000015b  DAC0_IRQHandler               
0000015b  DMA_IRQHandler                
0000015b  Default_Handler               
0000015b  GROUP0_IRQHandler             
0000015b  HardFault_Handler             
0000015b  I2C0_IRQHandler               
0000015b  I2C1_IRQHandler               
0000015b  NMI_Handler                   
0000015b  PendSV_Handler                
0000015b  RTC_IRQHandler                
0000015b  SPI0_IRQHandler               
0000015b  SPI1_IRQHandler               
0000015b  SVC_Handler                   
0000015b  TIMA0_IRQHandler              
0000015b  TIMA1_IRQHandler              
0000015b  TIMG0_IRQHandler              
0000015b  TIMG12_IRQHandler             
0000015b  TIMG6_IRQHandler              
0000015b  TIMG7_IRQHandler              
0000015b  TIMG8_IRQHandler              
0000015b  UART0_IRQHandler              
0000015b  UART1_IRQHandler              
0000015b  UART2_IRQHandler              
0000015b  UART3_IRQHandler              
0000015d  __TI_decompress_lzss          
000001d9  SYSCFG_DL_I2C_OLED_init       
00000200  __STACK_SIZE                  
0000022d  scheduler_run                 
00000271  SYSCFG_DL_SYSCTL_init         
000002b1  SysTick_Init                  
000002f1  __TI_auto_init_nobinit_nopinit
0000032d  SYSCFG_DL_GPIO_init           
00000361  SYSCFG_DL_initPower           
00000395  _c_int00_noargs               
000003bd  DL_I2C_setClockConfig         
000003e5  main                          
00000405  __TI_zero_init_nomemset       
0000041b  SYSCFG_DL_init                
00000431  scheduler_init                
00000445  __TI_decompress_none          
00000459  SysTick_Handler               
00000469  GROUP1_IRQHandler             
00000475  led_blink_task                
00000481  DL_Common_delayCycles         
0000048d  __aeabi_memcpy                
0000048d  __aeabi_memcpy4               
0000048d  __aeabi_memcpy8               
00000495  abort                         
0000049a  C$$EXIT                       
0000049b  HOSTexit                      
0000049f  Reset_Handler                 
000004a3  _system_pre_init              
000004c0  __TI_Handler_Table_Base       
000004cc  __TI_Handler_Table_Limit      
000004d4  __TI_CINIT_Base               
000004e4  __TI_CINIT_Limit              
000004e4  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
2020000c  sys_tick                      
20200010  task_num                      
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[88 symbols]

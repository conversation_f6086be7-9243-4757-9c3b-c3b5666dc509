******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jun 25 18:02:18 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000004cd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000640  0001f9c0  R  X
  SRAM                  20200000   00008000  00000211  00007def  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000640   00000640    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000528   00000528    r-x .text
  000005e8    000005e8    00000020   00000020    r-- .rodata
  00000608    00000608    00000038   00000038    r-- .cinit
20200000    20200000    00000011   00000000    rw-
  20200000    20200000    0000000c   00000000    rw- .data
  2020000c    2020000c    00000005   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000528     
                  000000c0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000001a8    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000242    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000244    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000002c0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000314    00000048     scheduler.o (.text.scheduler_init)
                  0000035c    00000044     scheduler.o (.text.scheduler_run)
                  000003a0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000003e0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000420    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000045c    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000494    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_1ms_init)
                  000004cc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000004f4    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000051a    00000002     --HOLE-- [fill = 0]
                  0000051c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000538    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000550    00000016     empty.o (.text.main)
                  00000566    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000057c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000058e    00000002     --HOLE-- [fill = 0]
                  00000590    00000010     empty.o (.text.SysTick_Handler)
                  000005a0    00000010     empty.o (.text.led_blink_task)
                  000005b0    0000000c     interrupt.o (.text.GROUP1_IRQHandler)
                  000005bc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000005c6    00000002     --HOLE-- [fill = 0]
                  000005c8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000005d0    00000006     libc.a : exit.c.obj (.text:abort)
                  000005d6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000005da    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000005de    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000005e2    00000006     --HOLE-- [fill = 0]

.cinit     0    00000608    00000038     
                  00000608    0000000d     (.cinit..data.load) [load image, compression = lzss]
                  00000615    00000003     --HOLE-- [fill = 0]
                  00000618    0000000c     (__TI_handler_table)
                  00000624    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000062c    00000010     (__TI_cinit_table)
                  0000063c    00000004     --HOLE-- [fill = 0]

.rodata    0    000005e8    00000020     
                  000005e8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_1msTimerConfig)
                  000005fc    00000003     ti_msp_dl_config.o (.rodata.gTIMER_1msClockConfig)
                  000005ff    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00000601    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    0000000c     UNINITIALIZED
                  20200000    0000000c     scheduler.o (.data.scheduler_task)

.bss       0    2020000c    00000005     UNINITIALIZED
                  2020000c    00000004     (.common:sys_tick)
                  20200010    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             348    25        0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       scheduler.o                    140    0         17     
       empty.o                        54     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         548    217       17     
                                                              
    .\System\
       interrupt.o                    12     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         12     0         0      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260    0         0      
       dl_i2c.o                       38     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         308    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      49        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1308   266       529    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000062c records: 2, size/record: 8, table size: 16
	.data: load addr=00000608, load size=0000000d bytes, run addr=20200000, run size=0000000c bytes, compression=lzss
	.bss: load addr=00000624, load size=00000008 bytes, run addr=2020000c, run size=00000005 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000618 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000243  ADC0_IRQHandler               
00000243  ADC1_IRQHandler               
00000243  AES_IRQHandler                
000005d6  C$$EXIT                       
00000243  CANFD0_IRQHandler             
00000243  DAC0_IRQHandler               
000005bd  DL_Common_delayCycles         
000004f5  DL_I2C_setClockConfig         
000000c1  DL_Timer_initTimerMode        
0000051d  DL_Timer_setClockConfig       
00000243  DMA_IRQHandler                
00000243  Default_Handler               
00000243  GROUP0_IRQHandler             
000005b1  GROUP1_IRQHandler             
000005d7  HOSTexit                      
00000243  HardFault_Handler             
00000243  I2C0_IRQHandler               
00000243  I2C1_IRQHandler               
00000243  NMI_Handler                   
00000243  PendSV_Handler                
00000243  RTC_IRQHandler                
000005db  Reset_Handler                 
00000243  SPI0_IRQHandler               
00000243  SPI1_IRQHandler               
00000243  SVC_Handler                   
0000045d  SYSCFG_DL_GPIO_init           
000002c1  SYSCFG_DL_I2C_OLED_init       
000003a1  SYSCFG_DL_SYSCTL_init         
00000495  SYSCFG_DL_TIMER_1ms_init      
00000539  SYSCFG_DL_init                
000003e1  SYSCFG_DL_initPower           
00000591  SysTick_Handler               
00000243  TIMA0_IRQHandler              
00000243  TIMA1_IRQHandler              
00000243  TIMG0_IRQHandler              
00000243  TIMG12_IRQHandler             
00000243  TIMG6_IRQHandler              
00000243  TIMG7_IRQHandler              
00000243  TIMG8_IRQHandler              
00000243  UART0_IRQHandler              
00000243  UART1_IRQHandler              
00000243  UART2_IRQHandler              
00000243  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
0000062c  __TI_CINIT_Base               
0000063c  __TI_CINIT_Limit              
0000063c  __TI_CINIT_Warm               
00000618  __TI_Handler_Table_Base       
00000624  __TI_Handler_Table_Limit      
00000421  __TI_auto_init_nobinit_nopinit
00000245  __TI_decompress_lzss          
0000057d  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000567  __TI_zero_init_nomemset       
000005c9  __aeabi_memcpy                
000005c9  __aeabi_memcpy4               
000005c9  __aeabi_memcpy8               
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
000004cd  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000005df  _system_pre_init              
000005d1  abort                         
ffffffff  binit                         
00000000  interruptVectors              
000005a1  led_blink_task                
00000551  main                          
000001a9  memcpy                        
00000315  scheduler_init                
0000035d  scheduler_run                 
2020000c  sys_tick                      
20200010  task_num                      


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  DL_Timer_initTimerMode        
000001a9  memcpy                        
00000200  __STACK_SIZE                  
00000243  ADC0_IRQHandler               
00000243  ADC1_IRQHandler               
00000243  AES_IRQHandler                
00000243  CANFD0_IRQHandler             
00000243  DAC0_IRQHandler               
00000243  DMA_IRQHandler                
00000243  Default_Handler               
00000243  GROUP0_IRQHandler             
00000243  HardFault_Handler             
00000243  I2C0_IRQHandler               
00000243  I2C1_IRQHandler               
00000243  NMI_Handler                   
00000243  PendSV_Handler                
00000243  RTC_IRQHandler                
00000243  SPI0_IRQHandler               
00000243  SPI1_IRQHandler               
00000243  SVC_Handler                   
00000243  TIMA0_IRQHandler              
00000243  TIMA1_IRQHandler              
00000243  TIMG0_IRQHandler              
00000243  TIMG12_IRQHandler             
00000243  TIMG6_IRQHandler              
00000243  TIMG7_IRQHandler              
00000243  TIMG8_IRQHandler              
00000243  UART0_IRQHandler              
00000243  UART1_IRQHandler              
00000243  UART2_IRQHandler              
00000243  UART3_IRQHandler              
00000245  __TI_decompress_lzss          
000002c1  SYSCFG_DL_I2C_OLED_init       
00000315  scheduler_init                
0000035d  scheduler_run                 
000003a1  SYSCFG_DL_SYSCTL_init         
000003e1  SYSCFG_DL_initPower           
00000421  __TI_auto_init_nobinit_nopinit
0000045d  SYSCFG_DL_GPIO_init           
00000495  SYSCFG_DL_TIMER_1ms_init      
000004cd  _c_int00_noargs               
000004f5  DL_I2C_setClockConfig         
0000051d  DL_Timer_setClockConfig       
00000539  SYSCFG_DL_init                
00000551  main                          
00000567  __TI_zero_init_nomemset       
0000057d  __TI_decompress_none          
00000591  SysTick_Handler               
000005a1  led_blink_task                
000005b1  GROUP1_IRQHandler             
000005bd  DL_Common_delayCycles         
000005c9  __aeabi_memcpy                
000005c9  __aeabi_memcpy4               
000005c9  __aeabi_memcpy8               
000005d1  abort                         
000005d6  C$$EXIT                       
000005d7  HOSTexit                      
000005db  Reset_Handler                 
000005df  _system_pre_init              
00000618  __TI_Handler_Table_Base       
00000624  __TI_Handler_Table_Limit      
0000062c  __TI_CINIT_Base               
0000063c  __TI_CINIT_Limit              
0000063c  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
2020000c  sys_tick                      
20200010  task_num                      
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[90 symbols]

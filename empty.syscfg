/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const I2C    = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1   = I2C.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                          = "Led";
GPIO1.associatedPins[0].$name        = "PIN_A14";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[0].assignedPin  = "14";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

I2C1.$name                             = "I2C_OLED";
I2C1.basicEnableController             = true;
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");

SYSCTL.forceDefaultClkConfig = true;

TIMER1.timerStartTimer    = true;
TIMER1.$name              = "TIMER_1ms";
TIMER1.timerClkDiv        = 8;
TIMER1.timerPeriod        = "1 ms";
TIMER1.timerMode          = "PERIODIC_UP";
TIMER1.interrupts         = ["ZERO"];
TIMER1.peripheral.$assign = "TIMG0";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution = "PA14";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
I2C1.peripheral.$suggestSolution             = "I2C0";
I2C1.peripheral.sdaPin.$suggestSolution      = "PA0";
I2C1.peripheral.sclPin.$suggestSolution      = "PA1";
SYSCTL.peripheral.$suggestSolution           = "SYSCTL";

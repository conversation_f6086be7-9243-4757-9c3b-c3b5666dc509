<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty.out -mempty.map -iD:/TI/CCS/mspm0_sdk_2_05_00_05/source -iD:/Projects/TI/empty -iD:/Projects/TI/empty/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./scheduler.o ./APP/oled_app.o ./BSP/oled_hardware_i2c.o ./System/clock.o ./System/interrupt.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x685bc92a</link_time>
   <link_errors>0x1</link_errors>
   <output_file>D:\Projects\TI\empty\Debug\empty.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x4cd</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>scheduler.o</file>
         <name>scheduler.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\Projects\TI\empty\Debug\.\APP\</path>
         <kind>object</kind>
         <file>oled_app.o</file>
         <name>oled_app.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\Projects\TI\empty\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>oled_hardware_i2c.o</file>
         <name>oled_hardware_i2c.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>clock.o</file>
         <name>clock.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>interrupt.o</file>
         <name>interrupt.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\Projects\TI\empty\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text:memcpy</name>
         <load_address>0x1a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a8</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x242</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x242</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x244</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x244</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0x2c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2c0</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text.scheduler_init</name>
         <load_address>0x314</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x314</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text.scheduler_run</name>
         <load_address>0x35c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x35c</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x3a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3a0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x3e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3e0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x420</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x420</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x45c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x45c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.SYSCFG_DL_TIMER_1ms_init</name>
         <load_address>0x494</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x494</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-59">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x4cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4cc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x4f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4f4</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x51c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x51c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x538</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x538</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-73">
         <name>.text.main</name>
         <load_address>0x550</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x550</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-50">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x566</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x566</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x57c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x57c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x590</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x590</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.led_blink_task</name>
         <load_address>0x5a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5a0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x5b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x5bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5bc</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-49">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x5c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5c8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text:abort</name>
         <load_address>0x5d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d0</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.HOSTexit</name>
         <load_address>0x5d6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x5da</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5da</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.text._system_pre_init</name>
         <load_address>0x5de</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5de</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-138">
         <name>.cinit..data.load</name>
         <load_address>0x608</load_address>
         <readonly>true</readonly>
         <run_address>0x608</run_address>
         <size>0xd</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-136">
         <name>__TI_handler_table</name>
         <load_address>0x618</load_address>
         <readonly>true</readonly>
         <run_address>0x618</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-139">
         <name>.cinit..bss.load</name>
         <load_address>0x624</load_address>
         <readonly>true</readonly>
         <run_address>0x624</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-137">
         <name>__TI_cinit_table</name>
         <load_address>0x62c</load_address>
         <readonly>true</readonly>
         <run_address>0x62c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-dc">
         <name>.rodata.gTIMER_1msTimerConfig</name>
         <load_address>0x5e8</load_address>
         <readonly>true</readonly>
         <run_address>0x5e8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-db">
         <name>.rodata.gTIMER_1msClockConfig</name>
         <load_address>0x5fc</load_address>
         <readonly>true</readonly>
         <run_address>0x5fc</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x5ff</load_address>
         <readonly>true</readonly>
         <run_address>0x5ff</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-100">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-be">
         <name>.data.scheduler_task</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.common:task_num</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200010</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-60">
         <name>.common:sys_tick</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020000c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x125</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_abbrev</name>
         <load_address>0x125</load_address>
         <run_address>0x125</run_address>
         <size>0x1b8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_abbrev</name>
         <load_address>0x2dd</load_address>
         <run_address>0x2dd</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_abbrev</name>
         <load_address>0x34a</load_address>
         <run_address>0x34a</run_address>
         <size>0x1ae</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_abbrev</name>
         <load_address>0x4f8</load_address>
         <run_address>0x4f8</run_address>
         <size>0x115</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_abbrev</name>
         <load_address>0x60d</load_address>
         <run_address>0x60d</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_abbrev</name>
         <load_address>0x66f</load_address>
         <run_address>0x66f</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_abbrev</name>
         <load_address>0x856</load_address>
         <run_address>0x856</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0xadc</load_address>
         <run_address>0xadc</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_abbrev</name>
         <load_address>0xb8b</load_address>
         <run_address>0xb8b</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_abbrev</name>
         <load_address>0xcfb</load_address>
         <run_address>0xcfb</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_abbrev</name>
         <load_address>0xd34</load_address>
         <run_address>0xd34</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_abbrev</name>
         <load_address>0xdf6</load_address>
         <run_address>0xdf6</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_abbrev</name>
         <load_address>0xe66</load_address>
         <run_address>0xe66</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_abbrev</name>
         <load_address>0xef3</load_address>
         <run_address>0xef3</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_abbrev</name>
         <load_address>0xf8b</load_address>
         <run_address>0xf8b</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_abbrev</name>
         <load_address>0xfb7</load_address>
         <run_address>0xfb7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_abbrev</name>
         <load_address>0xfde</load_address>
         <run_address>0xfde</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_abbrev</name>
         <load_address>0x1003</load_address>
         <run_address>0x1003</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x765</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_info</name>
         <load_address>0x765</load_address>
         <run_address>0x765</run_address>
         <size>0x24f2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2c57</load_address>
         <run_address>0x2c57</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_info</name>
         <load_address>0x2cd7</load_address>
         <run_address>0x2cd7</run_address>
         <size>0x4c9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_info</name>
         <load_address>0x31a0</load_address>
         <run_address>0x31a0</run_address>
         <size>0x229</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_info</name>
         <load_address>0x33c9</load_address>
         <run_address>0x33c9</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_info</name>
         <load_address>0x343e</load_address>
         <run_address>0x343e</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_info</name>
         <load_address>0x4100</load_address>
         <run_address>0x4100</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x7272</load_address>
         <run_address>0x7272</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_info</name>
         <load_address>0x7695</load_address>
         <run_address>0x7695</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_info</name>
         <load_address>0x7dd9</load_address>
         <run_address>0x7dd9</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_info</name>
         <load_address>0x7e1f</load_address>
         <run_address>0x7e1f</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0x7fb1</load_address>
         <run_address>0x7fb1</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x8077</load_address>
         <run_address>0x8077</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_info</name>
         <load_address>0x81f3</load_address>
         <run_address>0x81f3</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_info</name>
         <load_address>0x82eb</load_address>
         <run_address>0x82eb</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_info</name>
         <load_address>0x8326</load_address>
         <run_address>0x8326</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0x84bf</load_address>
         <run_address>0x84bf</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_info</name>
         <load_address>0x87b9</load_address>
         <run_address>0x87b9</run_address>
         <size>0x8e</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_ranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x58</load_address>
         <run_address>0x58</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_ranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_ranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_ranges</name>
         <load_address>0xb8</load_address>
         <run_address>0xb8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_ranges</name>
         <load_address>0x290</load_address>
         <run_address>0x290</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x468</load_address>
         <run_address>0x468</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_ranges</name>
         <load_address>0x4b0</load_address>
         <run_address>0x4b0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_ranges</name>
         <load_address>0x4f8</load_address>
         <run_address>0x4f8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_ranges</name>
         <load_address>0x510</load_address>
         <run_address>0x510</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_ranges</name>
         <load_address>0x560</load_address>
         <run_address>0x560</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_ranges</name>
         <load_address>0x578</load_address>
         <run_address>0x578</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x47c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_str</name>
         <load_address>0x47c</load_address>
         <run_address>0x47c</run_address>
         <size>0x1c94</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_str</name>
         <load_address>0x2110</load_address>
         <run_address>0x2110</run_address>
         <size>0x149</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_str</name>
         <load_address>0x2259</load_address>
         <run_address>0x2259</run_address>
         <size>0x4c7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_str</name>
         <load_address>0x2720</load_address>
         <run_address>0x2720</run_address>
         <size>0x209</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_str</name>
         <load_address>0x2929</load_address>
         <run_address>0x2929</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_str</name>
         <load_address>0x2a96</load_address>
         <run_address>0x2a96</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_str</name>
         <load_address>0x3345</load_address>
         <run_address>0x3345</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x5111</load_address>
         <run_address>0x5111</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_str</name>
         <load_address>0x5336</load_address>
         <run_address>0x5336</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_str</name>
         <load_address>0x5665</load_address>
         <run_address>0x5665</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_str</name>
         <load_address>0x575a</load_address>
         <run_address>0x575a</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_str</name>
         <load_address>0x58f5</load_address>
         <run_address>0x58f5</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_str</name>
         <load_address>0x5a5d</load_address>
         <run_address>0x5a5d</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_str</name>
         <load_address>0x5c32</load_address>
         <run_address>0x5c32</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_str</name>
         <load_address>0x5d7a</load_address>
         <run_address>0x5d7a</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_frame</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0xd0</load_address>
         <run_address>0xd0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_frame</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_frame</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_frame</name>
         <load_address>0x170</load_address>
         <run_address>0x170</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_frame</name>
         <load_address>0x190</load_address>
         <run_address>0x190</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_frame</name>
         <load_address>0x2bc</load_address>
         <run_address>0x2bc</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_frame</name>
         <load_address>0x6c4</load_address>
         <run_address>0x6c4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_frame</name>
         <load_address>0x754</load_address>
         <run_address>0x754</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_frame</name>
         <load_address>0x854</load_address>
         <run_address>0x854</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_frame</name>
         <load_address>0x874</load_address>
         <run_address>0x874</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x8ac</load_address>
         <run_address>0x8ac</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x8d4</load_address>
         <run_address>0x8d4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_frame</name>
         <load_address>0x904</load_address>
         <run_address>0x904</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_frame</name>
         <load_address>0x934</load_address>
         <run_address>0x934</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x203</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_line</name>
         <load_address>0x203</load_address>
         <run_address>0x203</run_address>
         <size>0x4c5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x6c8</load_address>
         <run_address>0x6c8</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_line</name>
         <load_address>0x784</load_address>
         <run_address>0x784</run_address>
         <size>0x25a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x9de</load_address>
         <run_address>0x9de</run_address>
         <size>0x201</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_line</name>
         <load_address>0xbdf</load_address>
         <run_address>0xbdf</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_line</name>
         <load_address>0xd57</load_address>
         <run_address>0xd57</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_line</name>
         <load_address>0x13d9</load_address>
         <run_address>0x13d9</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x2b47</load_address>
         <run_address>0x2b47</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_line</name>
         <load_address>0x2d23</load_address>
         <run_address>0x2d23</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_line</name>
         <load_address>0x323d</load_address>
         <run_address>0x323d</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_line</name>
         <load_address>0x327b</load_address>
         <run_address>0x327b</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x3379</load_address>
         <run_address>0x3379</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_line</name>
         <load_address>0x3439</load_address>
         <run_address>0x3439</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x3601</load_address>
         <run_address>0x3601</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_line</name>
         <load_address>0x3668</load_address>
         <run_address>0x3668</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_line</name>
         <load_address>0x36a9</load_address>
         <run_address>0x36a9</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_line</name>
         <load_address>0x374d</load_address>
         <run_address>0x374d</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x92</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_loc</name>
         <load_address>0x92</load_address>
         <run_address>0x92</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_loc</name>
         <load_address>0xd3</load_address>
         <run_address>0xd3</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_loc</name>
         <load_address>0xe6</load_address>
         <run_address>0xe6</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_loc</name>
         <load_address>0x438</load_address>
         <run_address>0x438</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_loc</name>
         <load_address>0x1e5f</load_address>
         <run_address>0x1e5f</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_loc</name>
         <load_address>0x1f37</load_address>
         <run_address>0x1f37</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_loc</name>
         <load_address>0x235b</load_address>
         <run_address>0x235b</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x24c7</load_address>
         <run_address>0x24c7</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_loc</name>
         <load_address>0x2536</load_address>
         <run_address>0x2536</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_loc</name>
         <load_address>0x269d</load_address>
         <run_address>0x269d</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x528</size>
         <contents>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x608</load_address>
         <run_address>0x608</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-137"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x5e8</load_address>
         <run_address>0x5e8</run_address>
         <size>0x20</size>
         <contents>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-e3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-100"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0xc</size>
         <contents>
            <object_component_ref idref="oc-be"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x2020000c</run_address>
         <size>0x5</size>
         <contents>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-60"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-13b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f7" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f8" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f9" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fa" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fb" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fc" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fe" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11a" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1012</size>
         <contents>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-13d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11c" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8847</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-13c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11e" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5a0</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-84"/>
         </contents>
      </logical_group>
      <logical_group id="lg-120" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5e63</size>
         <contents>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-e6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-122" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x954</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-c1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-124" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x37ed</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-85"/>
         </contents>
      </logical_group>
      <logical_group id="lg-126" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x26c3</size>
         <contents>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-9b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-130" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-87"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13a" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-145" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x640</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-146" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x11</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-147" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x640</used_space>
         <unused_space>0x1f9c0</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x528</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x5e8</start_address>
               <size>0x20</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x608</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x640</start_address>
               <size>0x1f9c0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x211</used_space>
         <unused_space>0x7def</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-fc"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-fe"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xc</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2020000c</start_address>
               <size>0x5</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200011</start_address>
               <size>0x7def</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x608</load_address>
            <load_size>0xd</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xc</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x624</load_address>
            <load_size>0x8</load_size>
            <run_address>0x2020000c</run_address>
            <run_size>0x5</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x62c</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x63c</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x63c</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x618</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x624</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-41">
         <name>SysTick_Handler</name>
         <value>0x591</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-42">
         <name>led_blink_task</name>
         <value>0x5a1</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-43">
         <name>main</name>
         <value>0x551</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-62">
         <name>SYSCFG_DL_init</name>
         <value>0x539</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-63">
         <name>SYSCFG_DL_initPower</name>
         <value>0x3e1</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-64">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x45d</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-65">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x3a1</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-66">
         <name>SYSCFG_DL_TIMER_1ms_init</name>
         <value>0x495</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-67">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0x2c1</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-72">
         <name>Default_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>Reset_Handler</name>
         <value>0x5db</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-74">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-75">
         <name>NMI_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>HardFault_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>SVC_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>PendSV_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>GROUP0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>TIMG8_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>UART3_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>ADC0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>ADC1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>CANFD0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>DAC0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>SPI0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>SPI1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>UART1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>UART2_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>UART0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>TIMG0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>TIMG6_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>TIMA0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>TIMA1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>TIMG7_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>TIMG12_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>I2C0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>I2C1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>AES_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>RTC_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>DMA_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9f">
         <name>scheduler_init</name>
         <value>0x315</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-a0">
         <name>task_num</name>
         <value>0x20200010</value>
      </symbol>
      <symbol id="sm-a1">
         <name>scheduler_run</name>
         <value>0x35d</value>
         <object_component_ref idref="oc-94"/>
      </symbol>
      <symbol id="sm-a2">
         <name>sys_tick</name>
         <value>0x2020000c</value>
      </symbol>
      <symbol id="sm-af">
         <name>GROUP1_IRQHandler</name>
         <value>0x5b1</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-b0">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b1">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b2">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b3">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b4">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b5">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b6">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b7">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b8">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-c1">
         <name>DL_Common_delayCycles</name>
         <value>0x5bd</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-cb">
         <name>DL_I2C_setClockConfig</name>
         <value>0x4f5</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-db">
         <name>DL_Timer_setClockConfig</name>
         <value>0x51d</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-dc">
         <name>DL_Timer_initTimerMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-e7">
         <name>_c_int00_noargs</name>
         <value>0x4cd</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-e8">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-f4">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x421</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-fc">
         <name>_system_pre_init</name>
         <value>0x5df</value>
         <object_component_ref idref="oc-6f"/>
      </symbol>
      <symbol id="sm-107">
         <name>__TI_zero_init_nomemset</name>
         <value>0x567</value>
         <object_component_ref idref="oc-50"/>
      </symbol>
      <symbol id="sm-110">
         <name>__TI_decompress_none</name>
         <value>0x57d</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-11b">
         <name>__TI_decompress_lzss</name>
         <value>0x245</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-125">
         <name>abort</name>
         <value>0x5d1</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-12f">
         <name>HOSTexit</name>
         <value>0x5d7</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-130">
         <name>C$$EXIT</name>
         <value>0x5d6</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-136">
         <name>__aeabi_memcpy</name>
         <value>0x5c9</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-137">
         <name>__aeabi_memcpy4</name>
         <value>0x5c9</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-138">
         <name>__aeabi_memcpy8</name>
         <value>0x5c9</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-152">
         <name>memcpy</name>
         <value>0x1a9</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-153">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-156">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-157">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link errors: red sections failed placement</title>
</link_info>

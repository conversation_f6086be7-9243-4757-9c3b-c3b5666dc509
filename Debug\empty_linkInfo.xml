<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty.out -mempty.map -iD:/TI/CCS/mspm0_sdk_2_05_00_05/source -iD:/Projects/TI/empty -iD:/Projects/TI/empty/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./scheduler.o ./APP/oled_app.o ./BSP/oled_hardware_i2c.o ./System/clock.o ./System/interrupt.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x685bdf70</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\Projects\TI\empty\Debug\empty.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x399</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>scheduler.o</file>
         <name>scheduler.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\Projects\TI\empty\Debug\.\APP\</path>
         <kind>object</kind>
         <file>oled_app.o</file>
         <name>oled_app.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\Projects\TI\empty\Debug\.\BSP\</path>
         <kind>object</kind>
         <file>oled_hardware_i2c.o</file>
         <name>oled_hardware_i2c.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>clock.o</file>
         <name>clock.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>interrupt.o</file>
         <name>interrupt.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\Projects\TI\empty\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-86">
         <name>.text:memcpy</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x15a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x15c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15c</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0x1d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d8</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text.scheduler_run</name>
         <load_address>0x22c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22c</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x270</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x270</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.SysTick_Init</name>
         <load_address>0x2b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2b0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x2f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x32c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x32c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x364</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x364</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x398</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x398</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x3c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3c0</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.text.main</name>
         <load_address>0x3e6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3e6</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x3fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3fc</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x412</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x412</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text.scheduler_init</name>
         <load_address>0x428</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x428</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x43c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x43c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x450</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x450</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.text.led_blink_task</name>
         <load_address>0x460</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x460</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x470</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x470</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x47c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x47c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x488</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x488</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text:abort</name>
         <load_address>0x490</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x490</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.HOSTexit</name>
         <load_address>0x496</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x496</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x49a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x49a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-69">
         <name>.text._system_pre_init</name>
         <load_address>0x49e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x49e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-134">
         <name>.cinit..data.load</name>
         <load_address>0x4b0</load_address>
         <readonly>true</readonly>
         <run_address>0x4b0</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-132">
         <name>__TI_handler_table</name>
         <load_address>0x4c0</load_address>
         <readonly>true</readonly>
         <run_address>0x4c0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-135">
         <name>.cinit..bss.load</name>
         <load_address>0x4cc</load_address>
         <readonly>true</readonly>
         <run_address>0x4cc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-133">
         <name>__TI_cinit_table</name>
         <load_address>0x4d4</load_address>
         <readonly>true</readonly>
         <run_address>0x4d4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-de">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x4a8</load_address>
         <readonly>true</readonly>
         <run_address>0x4a8</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-5c">
         <name>.data.sys_tick</name>
         <load_address>0x2020000c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020000c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.data.scheduler_task</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.common:task_num</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200010</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-137">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_abbrev</name>
         <load_address>0x10d</load_address>
         <run_address>0x10d</run_address>
         <size>0x1b8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x2c5</load_address>
         <run_address>0x2c5</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_abbrev</name>
         <load_address>0x332</load_address>
         <run_address>0x332</run_address>
         <size>0xe3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_abbrev</name>
         <load_address>0x415</load_address>
         <run_address>0x415</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x58e</load_address>
         <run_address>0x58e</run_address>
         <size>0x115</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_abbrev</name>
         <load_address>0x6a3</load_address>
         <run_address>0x6a3</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_abbrev</name>
         <load_address>0x705</load_address>
         <run_address>0x705</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x8ec</load_address>
         <run_address>0x8ec</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0x99b</load_address>
         <run_address>0x99b</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_abbrev</name>
         <load_address>0xb0b</load_address>
         <run_address>0xb0b</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0xb44</load_address>
         <run_address>0xb44</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0xc06</load_address>
         <run_address>0xc06</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0xc76</load_address>
         <run_address>0xc76</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_abbrev</name>
         <load_address>0xd03</load_address>
         <run_address>0xd03</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_abbrev</name>
         <load_address>0xd9b</load_address>
         <run_address>0xd9b</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_abbrev</name>
         <load_address>0xdc7</load_address>
         <run_address>0xdc7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_abbrev</name>
         <load_address>0xdee</load_address>
         <run_address>0xdee</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_abbrev</name>
         <load_address>0xe13</load_address>
         <run_address>0xe13</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x751</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_info</name>
         <load_address>0x751</load_address>
         <run_address>0x751</run_address>
         <size>0x1c6c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x23bd</load_address>
         <run_address>0x23bd</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_info</name>
         <load_address>0x243d</load_address>
         <run_address>0x243d</run_address>
         <size>0x156</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_info</name>
         <load_address>0x2593</load_address>
         <run_address>0x2593</run_address>
         <size>0x454</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x29e7</load_address>
         <run_address>0x29e7</run_address>
         <size>0x229</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_info</name>
         <load_address>0x2c10</load_address>
         <run_address>0x2c10</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_info</name>
         <load_address>0x2c85</load_address>
         <run_address>0x2c85</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x3947</load_address>
         <run_address>0x3947</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_info</name>
         <load_address>0x3d6a</load_address>
         <run_address>0x3d6a</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_info</name>
         <load_address>0x44ae</load_address>
         <run_address>0x44ae</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0x44f4</load_address>
         <run_address>0x44f4</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x4686</load_address>
         <run_address>0x4686</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x474c</load_address>
         <run_address>0x474c</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_info</name>
         <load_address>0x48c8</load_address>
         <run_address>0x48c8</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_info</name>
         <load_address>0x49c0</load_address>
         <run_address>0x49c0</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x49fb</load_address>
         <run_address>0x49fb</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_info</name>
         <load_address>0x4b94</load_address>
         <run_address>0x4b94</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_info</name>
         <load_address>0x4e8e</load_address>
         <run_address>0x4e8e</run_address>
         <size>0x8e</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_ranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_ranges</name>
         <load_address>0x78</load_address>
         <run_address>0x78</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_ranges</name>
         <load_address>0x98</load_address>
         <run_address>0x98</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_ranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x288</load_address>
         <run_address>0x288</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_ranges</name>
         <load_address>0x2d0</load_address>
         <run_address>0x2d0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x318</load_address>
         <run_address>0x318</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x330</load_address>
         <run_address>0x330</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_ranges</name>
         <load_address>0x380</load_address>
         <run_address>0x380</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_ranges</name>
         <load_address>0x398</load_address>
         <run_address>0x398</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x46c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_str</name>
         <load_address>0x46c</load_address>
         <run_address>0x46c</run_address>
         <size>0x170a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x1b76</load_address>
         <run_address>0x1b76</run_address>
         <size>0x149</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_str</name>
         <load_address>0x1cbf</load_address>
         <run_address>0x1cbf</run_address>
         <size>0x16f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_str</name>
         <load_address>0x1e2e</load_address>
         <run_address>0x1e2e</run_address>
         <size>0x494</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x22c2</load_address>
         <run_address>0x22c2</run_address>
         <size>0x209</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_str</name>
         <load_address>0x24cb</load_address>
         <run_address>0x24cb</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_str</name>
         <load_address>0x2638</load_address>
         <run_address>0x2638</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x2ee7</load_address>
         <run_address>0x2ee7</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_str</name>
         <load_address>0x310c</load_address>
         <run_address>0x310c</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_str</name>
         <load_address>0x343b</load_address>
         <run_address>0x343b</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_str</name>
         <load_address>0x3530</load_address>
         <run_address>0x3530</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x36cb</load_address>
         <run_address>0x36cb</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x3833</load_address>
         <run_address>0x3833</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_str</name>
         <load_address>0x3a08</load_address>
         <run_address>0x3a08</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_str</name>
         <load_address>0x3b50</load_address>
         <run_address>0x3b50</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_frame</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_frame</name>
         <load_address>0xd8</load_address>
         <run_address>0xd8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_frame</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_frame</name>
         <load_address>0x198</load_address>
         <run_address>0x198</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_frame</name>
         <load_address>0x1b8</load_address>
         <run_address>0x1b8</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x2e4</load_address>
         <run_address>0x2e4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_frame</name>
         <load_address>0x374</load_address>
         <run_address>0x374</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0x474</load_address>
         <run_address>0x474</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_frame</name>
         <load_address>0x494</load_address>
         <run_address>0x494</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x4cc</load_address>
         <run_address>0x4cc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x4f4</load_address>
         <run_address>0x4f4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_frame</name>
         <load_address>0x524</load_address>
         <run_address>0x524</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_frame</name>
         <load_address>0x554</load_address>
         <run_address>0x554</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x1e8</load_address>
         <run_address>0x1e8</run_address>
         <size>0x44e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x636</load_address>
         <run_address>0x636</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_line</name>
         <load_address>0x6f2</load_address>
         <run_address>0x6f2</run_address>
         <size>0x147</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_line</name>
         <load_address>0x839</load_address>
         <run_address>0x839</run_address>
         <size>0x279</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0xab2</load_address>
         <run_address>0xab2</run_address>
         <size>0x201</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_line</name>
         <load_address>0xcb3</load_address>
         <run_address>0xcb3</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_line</name>
         <load_address>0xe2b</load_address>
         <run_address>0xe2b</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x14ad</load_address>
         <run_address>0x14ad</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0x1689</load_address>
         <run_address>0x1689</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_line</name>
         <load_address>0x1ba3</load_address>
         <run_address>0x1ba3</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x1be1</load_address>
         <run_address>0x1be1</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x1cdf</load_address>
         <run_address>0x1cdf</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x1d9f</load_address>
         <run_address>0x1d9f</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x1f67</load_address>
         <run_address>0x1f67</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_line</name>
         <load_address>0x1fce</load_address>
         <run_address>0x1fce</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0x200f</load_address>
         <run_address>0x200f</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_line</name>
         <load_address>0x20b3</load_address>
         <run_address>0x20b3</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x92</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_loc</name>
         <load_address>0x92</load_address>
         <run_address>0x92</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_loc</name>
         <load_address>0xd3</load_address>
         <run_address>0xd3</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_loc</name>
         <load_address>0x13b</load_address>
         <run_address>0x13b</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_loc</name>
         <load_address>0x14e</load_address>
         <run_address>0x14e</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x4a0</load_address>
         <run_address>0x4a0</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_loc</name>
         <load_address>0x578</load_address>
         <run_address>0x578</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x99c</load_address>
         <run_address>0x99c</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0xb08</load_address>
         <run_address>0xb08</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0xb77</load_address>
         <run_address>0xb77</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_loc</name>
         <load_address>0xcde</load_address>
         <run_address>0xcde</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x3e8</size>
         <contents>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-69"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x4b0</load_address>
         <run_address>0x4b0</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-133"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x4a8</load_address>
         <run_address>0x4a8</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-de"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-fc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x10</size>
         <contents>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-c2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200010</run_address>
         <size>0x1</size>
         <contents>
            <object_component_ref idref="oc-c1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-137"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f3" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f4" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f5" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f6" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f7" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f8" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-fa" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-116" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe22</size>
         <contents>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-139"/>
         </contents>
      </logical_group>
      <logical_group id="lg-118" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4f1c</size>
         <contents>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-138"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11a" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3c0</size>
         <contents>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-84"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11c" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3c39</size>
         <contents>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-e4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11e" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x574</size>
         <contents>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-c6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-120" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2153</size>
         <contents>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-85"/>
         </contents>
      </logical_group>
      <logical_group id="lg-122" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd04</size>
         <contents>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-9b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12c" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-136" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-141" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4e8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-142" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x11</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-143" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x4e8</used_space>
         <unused_space>0x1fb18</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x3e8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x4a8</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x4b0</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x4e8</start_address>
               <size>0x1fb18</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x211</used_space>
         <unused_space>0x7def</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-f8"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-fa"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x10</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200010</start_address>
               <size>0x1</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200011</start_address>
               <size>0x7def</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x4b0</load_address>
            <load_size>0xe</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x10</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x4cc</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200010</run_address>
            <run_size>0x1</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x4d4</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x4e4</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x4e4</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x4c0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x4cc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3e">
         <name>led_blink_task</name>
         <value>0x461</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-3f">
         <name>main</name>
         <value>0x3e7</value>
         <object_component_ref idref="oc-6d"/>
      </symbol>
      <symbol id="sm-57">
         <name>SYSCFG_DL_init</name>
         <value>0x413</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-58">
         <name>SYSCFG_DL_initPower</name>
         <value>0x365</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-59">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x32d</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-5a">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x271</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-5b">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0x1d9</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-66">
         <name>Default_Handler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-67">
         <name>Reset_Handler</name>
         <value>0x49b</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-68">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-69">
         <name>NMI_Handler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6a">
         <name>HardFault_Handler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6b">
         <name>SVC_Handler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6c">
         <name>PendSV_Handler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6d">
         <name>GROUP0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6e">
         <name>TIMG8_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>UART3_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>ADC0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>ADC1_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>CANFD0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>DAC0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>SPI0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>SPI1_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>UART1_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>UART2_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>UART0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>TIMG0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>TIMG6_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>TIMA0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>TIMA1_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>TIMG7_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>TIMG12_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>I2C0_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>I2C1_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>AES_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>RTC_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>DMA_IRQHandler</name>
         <value>0x15b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>scheduler_init</name>
         <value>0x429</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-94">
         <name>task_num</name>
         <value>0x20200010</value>
      </symbol>
      <symbol id="sm-95">
         <name>scheduler_run</name>
         <value>0x22d</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-96">
         <name>sys_tick</name>
         <value>0x2020000c</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-a3">
         <name>SysTick_Init</name>
         <value>0x2b1</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-b0">
         <name>SysTick_Handler</name>
         <value>0x451</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-b1">
         <name>GROUP1_IRQHandler</name>
         <value>0x471</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-b2">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b3">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b4">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b5">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b6">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b7">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b8">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-b9">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ba">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-c3">
         <name>DL_Common_delayCycles</name>
         <value>0x47d</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-cd">
         <name>DL_I2C_setClockConfig</name>
         <value>0x3c1</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-d8">
         <name>_c_int00_noargs</name>
         <value>0x399</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-d9">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-e5">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x2f1</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-ed">
         <name>_system_pre_init</name>
         <value>0x49f</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-f8">
         <name>__TI_zero_init_nomemset</name>
         <value>0x3fd</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-101">
         <name>__TI_decompress_none</name>
         <value>0x43d</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-10c">
         <name>__TI_decompress_lzss</name>
         <value>0x15d</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-116">
         <name>abort</name>
         <value>0x491</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-120">
         <name>HOSTexit</name>
         <value>0x497</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-121">
         <name>C$$EXIT</name>
         <value>0x496</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-127">
         <name>__aeabi_memcpy</name>
         <value>0x489</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-128">
         <name>__aeabi_memcpy4</name>
         <value>0x489</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-129">
         <name>__aeabi_memcpy8</name>
         <value>0x489</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-143">
         <name>memcpy</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-144">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-147">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-148">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>

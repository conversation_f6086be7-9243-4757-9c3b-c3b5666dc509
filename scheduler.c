#include "scheduler.h"
#include "System/clock.h"

// 外部函数声明
extern void led_blink_task(void);
// 使用System文件夹中的tick_ms作为时基
extern volatile unsigned long tick_ms;

uint8_t task_num;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;

static task_t scheduler_task[] =
{
    {led_blink_task, 500, 0},  // LED闪烁任务，每500ms执行一次
};

void scheduler_init(void)
{
    // 初始化1ms定时器（替代SysTick）
    Timer_1ms_Init();

    // 计算任务数量
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = (uint32_t)tick_ms;  // 使用tick_ms作为时基

        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}



/*
 * 系统统一时间管理
 * 使用SysTick提供1ms时基，供调度器和OLED驱动共同使用
 */

#ifndef _SYSTEM_TIME_H_
#define _SYSTEM_TIME_H_

#include <stdint.h>

// 统一的系统时间变量（1ms递增）
extern volatile uint32_t system_tick_ms;

// 系统时间初始化函数
void SystemTime_Init(void);

// 延时函数（毫秒）
void SystemTime_DelayMs(uint32_t ms);

// 获取当前时间（毫秒）
uint32_t SystemTime_GetMs(void);

// 兼容OLED驱动的函数
int mspm0_delay_ms(unsigned long num_ms);
int mspm0_get_clock_ms(unsigned long *count);

// 兼容旧版本的函数名
void SysTick_Init(void);

#endif /* _SYSTEM_TIME_H_ */

/*
 * 简单的LED闪烁测试程序
 * 用于验证基本的GPIO和SysTick功能
 */

#include "ti_msp_dl_config.h"

// 全局计数器
volatile uint32_t tick_count = 0;

// SysTick中断服务程序
void SysTick_Handler(void)
{
    tick_count++;
    
    // 每500ms切换一次LED状态
    if (tick_count % 500 == 0) {
        DL_GPIO_togglePins(Led_PORT, Led_PIN_A3_PIN);
    }
}

int main(void)
{
    // 初始化系统
    SYSCFG_DL_init();
    
    // 配置SysTick定时器，每1ms产生一次中断
    // 系统时钟32MHz，1ms需要32000个时钟周期
    DL_SYSTICK_config(32000 - 1);
    
    // 启用SysTick中断
    DL_SYSTICK_enableInterrupt();
    
    // 启动SysTick定时器
    DL_SYSTICK_enable();
    
    // 主循环
    while (1) 
    {
        // 空循环，所有工作都在中断中完成
        // 可以在这里添加其他非时间相关的任务
    }
}

/*
 * 使用说明：
 * 1. 将此文件内容复制到empty.c中替换原有内容
 * 2. 编译并下载到开发板
 * 3. 观察LED是否以1Hz频率闪烁（每500ms切换一次状态）
 * 4. 如果LED正常闪烁，说明基本功能正常，可以逐步恢复调度器功能
 * 5. 如果LED仍不闪烁，检查：
 *    - GPIO配置是否正确
 *    - SysTick配置是否正确
 *    - 中断是否正常工作
 */

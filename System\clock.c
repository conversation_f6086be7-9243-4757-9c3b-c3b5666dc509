#include "ti_msp_dl_config.h"
#include "clock.h"

volatile unsigned long tick_ms;
volatile uint32_t start_time;

int mspm0_delay_ms(unsigned long num_ms)
{
    start_time = tick_ms;
    while (tick_ms - start_time < num_ms);
    return 0;
}

int mspm0_get_clock_ms(unsigned long *count)
{
    if (!count)
        return 1;
    count[0] = tick_ms;
    return 0;
}

// 使用TIMER_1ms替代SysTick初始化
void Timer_1ms_Init(void)
{
    // TIMER_1ms已经在ti_msp_dl_config.c中配置好了
    // 这里只需要启用中断
    NVIC_SetPriority(TIMER_1ms_INST_INT_IRQN, 0);
    NVIC_EnableIRQ(TIMER_1ms_INST_INT_IRQN);
}

// 保持兼容性的函数名
void SysTick_Init(void)
{
    Timer_1ms_Init();
}
